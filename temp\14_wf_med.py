import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup
import time
import urllib.parse

# 设置 headless Chrome
options = Options()
options.add_argument('--headless')
options.add_argument('--disable-gpu')
options.add_argument('--no-sandbox')
options.add_argument('--disable-dev-shm-usage')
options.add_argument('--disable-blink-features=AutomationControlled')
options.add_experimental_option("excludeSwitches", ["enable-automation"])
options.add_experimental_option('useAutomationExtension', False)
options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")

driver = webdriver.Chrome(options=options)

# 反反爬设置
driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {
    "source": """
    Object.defineProperty(navigator, 'webdriver', {
      get: () => undefined
    });
    """
})

# 搜索关键词
search_keyword = "奶"
base_url = "https://med.wanfangdata.com.cn/Paper/Search"

# 遍历第1-2页
for page in range(1, 3):
    # 构建搜索URL
    search_url = f"{base_url}?q={urllib.parse.quote(search_keyword)}&SearchMode=Default&p={page}"
    print(f"\n正在抓取第 {page} 页: {search_url}")

    driver.get(search_url)
    time.sleep(5)
    soup = BeautifulSoup(driver.page_source, "lxml")

    # 查找搜索结果项
    items = soup.select("div.item")

    if not items:
        print(f"第 {page} 页没有找到数据，跳过")
        continue

    print(f"第 {page} 页找到 {len(items)} 条结果")

    for item in items:
        try:
            # 提取标题和链接
            title_link = item.select_one("div.item-title a")
            if not title_link:
                continue

            title = title_link.get_text(strip=True)
            # 清理标题中的HTML标签和序号
            title = re.sub(r'^\d+\.\s*', '', title)  # 去掉开头的序号
            title = re.sub(r'<[^>]+>', '', title)    # 去掉HTML标签

            article_url = title_link.get("href", "")
            if not article_url.startswith("http"):
                article_url = "https://med.wanfangdata.com.cn" + article_url

            # 获取data-itemid和data-dbid
            checkbox = item.select_one("input.item-checkbox")
            item_id = checkbox.get("data-itemid", "") if checkbox else ""
            db_id = checkbox.get("data-dbid", "") if checkbox else ""

            print(f"找到文章: {title}")
            print(f"链接: {article_url}")

            # 访问文章详情页
            if article_url:
                driver.get(article_url)
                time.sleep(3)
                detail_soup = BeautifulSoup(driver.page_source, "lxml")

                # 提取摘要 - 根据万方医学网的HTML结构
                abstract = "无摘要"
                # 查找div.abstracts下的p#abstractWording
                abstract_element = detail_soup.select_one("div.abstracts p#abstractWording")
                if abstract_element:
                    abstract_text = abstract_element.get_text(strip=True)
                    # 去掉开头的"摘要"字样
                    if abstract_text.startswith("摘要"):
                        abstract_text = abstract_text[2:].strip()
                    # 处理HTML实体编码
                    abstract_text = abstract_text.replace("&lt;", "<").replace("&gt;", ">").replace("&amp;", "&")
                    if abstract_text and len(abstract_text) > 10:
                        abstract = abstract_text

                # 提取发布日期 - 根据万方医学网的HTML结构
                publish_date = "无日期"
                # 查找包含"发布时间"的表格行
                date_rows = detail_soup.select("div.table-tr")
                for row in date_rows:
                    th_element = row.select_one("span.table-th")
                    if th_element and "发布时间" in th_element.get_text():
                        td_element = row.select_one("span.table-td em")
                        if td_element:
                            publish_date = td_element.get_text(strip=True)
                            break

                # 打印格式化信息
                print("信息来源：万方医学数据库")
                print(f"任务标题：{title}")
                print(f"任务摘要：{abstract}")
                print(f"发布日期：{publish_date}")
                print(f"原始链接：{article_url}")
                print("通报国别：中国")
                print("产地国别：中国")
                if item_id:
                    print(f"文章ID：{item_id}")
                if db_id:
                    print(f"数据库ID：{db_id}")
                print("=" * 60)

        except Exception as e:
            print(f"处理文章时出错: {e}")
            continue

driver.quit()
print("\n数据抓取完成")